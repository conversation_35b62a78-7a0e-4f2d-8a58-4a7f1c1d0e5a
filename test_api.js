// API连接测试脚本
// 在浏览器控制台中运行此脚本来测试API连接

async function testApiConnection() {
    console.log('🔍 开始测试API连接...');
    
    const baseURL = 'http://localhost:8080/api/v1';
    
    // 测试基本连接
    try {
        console.log('📡 测试基本连接...');
        const response = await fetch(`${baseURL}/system/ping`);
        console.log('连接状态:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API服务正常:', data);
        } else {
            console.log('❌ API服务响应异常:', response.status);
        }
    } catch (error) {
        console.log('❌ API服务连接失败:', error.message);
        console.log('💡 可能的原因:');
        console.log('   1. 后端服务未启动 (需要在8080端口启动)');
        console.log('   2. CORS跨域问题');
        console.log('   3. 网络连接问题');
    }
    
    // 测试用户登录接口
    try {
        console.log('📡 测试用户登录接口...');
        const response = await fetch(`${baseURL}/user/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phone: '13800138000',
                password: 'password123'
            })
        });
        
        console.log('登录接口状态:', response.status);
        const data = await response.json();
        console.log('登录接口响应:', data);
        
    } catch (error) {
        console.log('❌ 登录接口测试失败:', error.message);
    }
    
    // 测试管理员登录接口
    try {
        console.log('📡 测试管理员登录接口...');
        const response = await fetch(`${baseURL}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phone: '15688515913',
                password: 'admin888'
            })
        });
        
        console.log('管理员登录状态:', response.status);
        const data = await response.json();
        console.log('管理员登录响应:', data);
        
    } catch (error) {
        console.log('❌ 管理员登录测试失败:', error.message);
    }
    
    console.log('🏁 API连接测试完成');
}

// 自动运行测试
testApiConnection();

// 导出测试函数供手动调用
window.testApiConnection = testApiConnection;
