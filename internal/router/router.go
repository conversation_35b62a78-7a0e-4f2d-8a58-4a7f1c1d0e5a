package router

import (
	"solve_api_admin/internal/config"
	"solve_api_admin/internal/handler"
	"solve_api_admin/internal/middleware"
	"solve_api_admin/internal/service"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Setup 设置路由
func Setup(services *service.Services, cfg *config.Config) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	r := gin.New()

	// 中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// CORS配置
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowHeaders = []string{"*"}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	r.Use(cors.New(corsConfig))

	// 创建处理器
	userHandler := handler.NewUserHandler(services.User, services.SMS)
	appHandler := handler.NewAppHandler(services.App)
	searchHandler := handler.NewSearchHandler(services.Search, services.App)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 用户认证相关
		user := api.Group("/user")
		{
			user.POST("/register", userHandler.Register)
			user.POST("/login", userHandler.Login)
			user.POST("/send-code", userHandler.SendCode)
			user.POST("/forgot-password", userHandler.SendResetCode)
			user.POST("/reset-password", userHandler.ResetPassword)

			// 需要认证的用户接口
			userAuth := user.Use(middleware.AuthMiddleware(cfg.JWT.Secret))
			{
				userAuth.GET("/info", userHandler.GetUserInfo)
				userAuth.PUT("/:id/change-password", userHandler.ChangePassword)
				userAuth.GET("/:id/balance", userHandler.GetBalance)
				userAuth.POST("/:id/recharge", userHandler.Recharge)
				userAuth.GET("/:id/balance/logs", userHandler.GetBalanceLogs)

				// 应用管理
				userAuth.POST("/:id/app", appHandler.CreateApp)
				userAuth.GET("/:id/app", appHandler.GetUserApps)
				userAuth.GET("/:id/app/:app_id", appHandler.GetAppDetail)
				userAuth.PUT("/:id/app/:app_id", appHandler.UpdateApp)
				userAuth.PUT("/:id/app/:app_id/reset-secret", appHandler.ResetAppSecret)
				userAuth.PUT("/:id/app/:app_id/status", appHandler.UpdateAppStatus)
			}
		}

		// 管理员相关
		admin := api.Group("/admin")
		{
			admin.POST("/login", userHandler.AdminLogin)
			admin.POST("/forgot-password", userHandler.SendAdminResetCode)
			admin.POST("/reset-password", userHandler.AdminResetPassword)

			// 需要管理员权限的接口
			adminAuth := admin.Use(middleware.AuthMiddleware(cfg.JWT.Secret), middleware.AdminMiddleware())
			{
				adminAuth.GET("/users", userHandler.GetUserList)
				adminAuth.PUT("/users/:id/status", userHandler.UpdateUserStatus)
			}
		}

		// 拍照搜题API
		apiGroup := api.Group("/api")
		{
			apiGroup.POST("/search", middleware.AppAuthMiddleware(services.App), searchHandler.Search)
		}
	}

	return r
}
