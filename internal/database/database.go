package database

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"solve_api_admin/internal/config"
	"solve_api_admin/internal/model"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Init 初始化数据库
func Init(dsn string) (*gorm.DB, error) {
	// 确保数据目录存在
	dir := filepath.Dir(dsn)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建数据目录失败: %v", err)
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 自动迁移
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %v", err)
	}

	// 初始化测试数据
	if err := initTestData(db); err != nil {
		log.Printf("初始化测试数据失败: %v", err)
	}

	log.Println("数据库初始化成功")
	return db, nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&model.User{},
		&model.App{},
		&model.BalanceLog{},
		&model.SearchLog{},
		&model.VerifyCode{},
	)
}

// initTestData 初始化测试数据
func initTestData(db *gorm.DB) error {
	// 检查是否已有测试用户
	var count int64
	db.Model(&model.User{}).Count(&count)
	if count > 0 {
		return nil // 已有数据，跳过初始化
	}

	// 创建测试用户 (password123)
	testUser := &model.User{
		Phone:    "13800138000",
		Password: "$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPFmcxlQ6UpOXaOlOXaOlOXaOlOXa", // password123
		Balance:  100.50,
		Status:   1,
		Role:     "user",
	}

	// 创建管理员用户 (admin888)
	adminUser := &model.User{
		Phone:    "15688515913",
		Password: "$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPFmcxlQ6UpOXaOlOXaOlOXaOlOXa", // admin888
		Balance:  1000.00,
		Status:   1,
		Role:     "admin",
	}

	if err := db.Create(testUser).Error; err != nil {
		return fmt.Errorf("创建测试用户失败: %v", err)
	}

	if err := db.Create(adminUser).Error; err != nil {
		return fmt.Errorf("创建管理员用户失败: %v", err)
	}

	log.Println("测试数据初始化成功")
	return nil
}

// InitRedis 初始化Redis连接
func InitRedis(cfg config.RedisConfig) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Addr,
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Printf("Redis连接失败: %v", err)
		return nil
	}

	log.Println("Redis连接成功")
	return rdb
}
