package model

import (
	"time"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	Phone     string         `json:"phone" gorm:"uniqueIndex;size:11;not null"`
	Password  string         `json:"-" gorm:"size:255;not null"`
	Balance   float64        `json:"balance" gorm:"type:decimal(10,2);default:0"`
	Status    int            `json:"status" gorm:"default:1;comment:1=正常,2=冻结"`
	Role      string         `json:"role" gorm:"size:20;default:user;comment:user=用户,admin=管理员"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Apps        []App        `json:"apps,omitempty" gorm:"foreignKey:UserID"`
	BalanceLogs []BalanceLog `json:"balance_logs,omitempty" gorm:"foreignKey:UserID"`
}

// App 应用模型
type App struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	UserID    uint           `json:"user_id" gorm:"not null;index"`
	Name      string         `json:"name" gorm:"size:50;not null"`
	Type      int            `json:"type" gorm:"default:1;comment:1=拍照搜题"`
	AppKey    string         `json:"app_key" gorm:"uniqueIndex;size:32;not null"`
	SecretKey string         `json:"secret_key" gorm:"size:64;not null"`
	Status    int            `json:"status" gorm:"default:1;comment:1=正常,2=冻结"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	User        User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	SearchLogs  []SearchLog `json:"search_logs,omitempty" gorm:"foreignKey:AppID"`
}

// BalanceLog 余额记录模型
type BalanceLog struct {
	ID          uint      `json:"id" gorm:"primarykey"`
	UserID      uint      `json:"user_id" gorm:"not null;index"`
	Amount      float64   `json:"amount" gorm:"type:decimal(10,2);not null"`
	Balance     float64   `json:"balance" gorm:"type:decimal(10,2);not null"`
	Type        int       `json:"type" gorm:"not null;comment:1=充值,2=消费,3=退款"`
	Description string    `json:"description" gorm:"size:255"`
	CreatedAt   time.Time `json:"created_at"`
	
	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// SearchLog 搜题记录模型
type SearchLog struct {
	ID        uint      `json:"id" gorm:"primarykey"`
	AppID     uint      `json:"app_id" gorm:"not null;index"`
	UserID    uint      `json:"user_id" gorm:"not null;index"`
	Question  string    `json:"question" gorm:"type:text"`
	Answer    string    `json:"answer" gorm:"type:text"`
	Analysis  string    `json:"analysis" gorm:"type:text"`
	ImageURL  string    `json:"image_url" gorm:"size:500"`
	Cost      float64   `json:"cost" gorm:"type:decimal(10,2);not null"`
	Cached    bool      `json:"cached" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联
	App  App  `json:"app,omitempty" gorm:"foreignKey:AppID"`
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// VerifyCode 验证码模型
type VerifyCode struct {
	ID        uint      `json:"id" gorm:"primarykey"`
	Phone     string    `json:"phone" gorm:"size:11;not null;index"`
	Code      string    `json:"code" gorm:"size:6;not null"`
	Type      string    `json:"type" gorm:"size:20;not null;comment:register=注册,reset=重置密码"`
	Used      bool      `json:"used" gorm:"default:false"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
}

// TableName 设置表名
func (User) TableName() string {
	return "users"
}

func (App) TableName() string {
	return "apps"
}

func (BalanceLog) TableName() string {
	return "balance_logs"
}

func (SearchLog) TableName() string {
	return "search_logs"
}

func (VerifyCode) TableName() string {
	return "verify_codes"
}
