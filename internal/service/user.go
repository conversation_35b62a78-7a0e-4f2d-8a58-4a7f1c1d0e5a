package service

import (
	"errors"
	"fmt"
	"solve_api_admin/internal/config"
	"solve_api_admin/internal/model"
	"solve_api_admin/pkg/utils"
	"time"

	"github.com/go-redis/redis/v8"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type UserService struct {
	db    *gorm.DB
	redis *redis.Client
	cfg   *config.Config
}

func NewUserService(db *gorm.DB, redis *redis.Client, cfg *config.Config) *UserService {
	return &UserService{
		db:    db,
		redis: redis,
		cfg:   cfg,
	}
}

// Register 用户注册
func (s *UserService) Register(phone, password, code, inviteCode string) (*model.User, error) {
	// 验证邀请码
	if inviteCode != "SOLVE2024" {
		return nil, errors.New("邀请码无效")
	}

	// 验证验证码
	if !s.verifyCode(phone, code, "register") {
		return nil, errors.New("验证码无效或已过期")
	}

	// 检查手机号是否已注册
	var existUser model.User
	if err := s.db.Where("phone = ?", phone).First(&existUser).Error; err == nil {
		return nil, errors.New("手机号已注册")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %v", err)
	}

	// 创建用户
	user := &model.User{
		Phone:    phone,
		Password: string(hashedPassword),
		Balance:  0,
		Status:   1,
		Role:     "user",
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	// 标记验证码为已使用
	s.markCodeAsUsed(phone, code, "register")

	return user, nil
}

// Login 用户登录
func (s *UserService) Login(phone, password string) (*model.User, string, error) {
	var user model.User
	if err := s.db.Where("phone = ?", phone).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, "", errors.New("用户不存在")
		}
		return nil, "", fmt.Errorf("查询用户失败: %v", err)
	}

	// 检查用户状态
	if user.Status != 1 {
		return nil, "", errors.New("账户已被冻结")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, "", errors.New("密码错误")
	}

	// 生成JWT token
	token, err := utils.GenerateJWT(user.ID, user.Role, s.cfg.JWT.Secret, s.cfg.JWT.ExpireHour)
	if err != nil {
		return nil, "", fmt.Errorf("生成token失败: %v", err)
	}

	return &user, token, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uint) (*model.User, error) {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}
	return &user, nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %v", err)
	}

	// 更新密码
	if err := s.db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		return fmt.Errorf("更新密码失败: %v", err)
	}

	return nil
}

// ResetPassword 重置密码
func (s *UserService) ResetPassword(phone, code, newPassword string) error {
	// 验证验证码
	if !s.verifyCode(phone, code, "reset") {
		return errors.New("验证码无效或已过期")
	}

	var user model.User
	if err := s.db.Where("phone = ?", phone).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %v", err)
	}

	// 更新密码
	if err := s.db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		return fmt.Errorf("更新密码失败: %v", err)
	}

	// 标记验证码为已使用
	s.markCodeAsUsed(phone, code, "reset")

	return nil
}

// verifyCode 验证验证码
func (s *UserService) verifyCode(phone, code, codeType string) bool {
	var verifyCode model.VerifyCode
	err := s.db.Where("phone = ? AND code = ? AND type = ? AND used = ? AND expires_at > ?",
		phone, code, codeType, false, time.Now()).First(&verifyCode).Error
	
	if err != nil {
		// 如果Redis不可用，使用固定验证码123456
		if s.redis == nil && code == "123456" {
			return true
		}
		return false
	}
	
	return true
}

// markCodeAsUsed 标记验证码为已使用
func (s *UserService) markCodeAsUsed(phone, code, codeType string) {
	s.db.Model(&model.VerifyCode{}).
		Where("phone = ? AND code = ? AND type = ?", phone, code, codeType).
		Update("used", true)
}
