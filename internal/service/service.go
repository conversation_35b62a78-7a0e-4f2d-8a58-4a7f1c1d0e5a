package service

import (
	"solve_api_admin/internal/config"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// Services 服务集合
type Services struct {
	User   *UserService
	App    *AppService
	Search *SearchService
	SMS    *SMSService
}

// NewServices 创建服务实例
func NewServices(db *gorm.DB, redis *redis.Client, cfg *config.Config) *Services {
	smsService := NewSMSService(redis, cfg)
	smsService.SetDB(db)

	return &Services{
		User:   NewUserService(db, redis, cfg),
		App:    NewAppService(db, cfg),
		Search: NewSearchService(db, cfg),
		SMS:    smsService,
	}
}
