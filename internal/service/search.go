package service

import (
	"errors"
	"fmt"
	"solve_api_admin/internal/config"
	"solve_api_admin/internal/model"

	"gorm.io/gorm"
)

type SearchService struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewSearchService(db *gorm.DB, cfg *config.Config) *SearchService {
	return &SearchService{
		db:  db,
		cfg: cfg,
	}
}

// SearchRequest 搜题请求
type SearchRequest struct {
	ImageURL string `json:"image_url" binding:"required"`
}

// SearchResponse 搜题响应
type SearchResponse struct {
	ID       uint    `json:"id"`
	Question string  `json:"question"`
	Answer   string  `json:"answer"`
	Analysis string  `json:"analysis"`
	Cost     float64 `json:"cost"`
	Cached   bool    `json:"cached"`
}

// Search 拍照搜题
func (s *SearchService) Search(app *model.App, imageURL string) (*SearchResponse, error) {
	// 获取用户信息
	var user model.User
	if err := s.db.First(&user, app.UserID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 检查余额
	cost := 0.1 // 每次搜题费用0.1元
	if user.Balance < cost {
		return nil, errors.New("余额不足，请先充值")
	}

	// 模拟搜题结果
	question := "解这个方程：2x + 3 = 7"
	answer := "解：2x + 3 = 7\n2x = 7 - 3\n2x = 4\nx = 2"
	analysis := "这是一个一元一次方程，通过移项和化简可以求解。"

	// 扣费
	if err := s.deductBalance(user.ID, cost); err != nil {
		return nil, fmt.Errorf("扣费失败: %v", err)
	}

	// 记录搜题日志
	searchLog := &model.SearchLog{
		AppID:    app.ID,
		UserID:   app.UserID,
		Question: question,
		Answer:   answer,
		Analysis: analysis,
		ImageURL: imageURL,
		Cost:     cost,
		Cached:   false,
	}

	if err := s.db.Create(searchLog).Error; err != nil {
		return nil, fmt.Errorf("记录搜题日志失败: %v", err)
	}

	return &SearchResponse{
		ID:       searchLog.ID,
		Question: question,
		Answer:   answer,
		Analysis: analysis,
		Cost:     cost,
		Cached:   false,
	}, nil
}

// deductBalance 扣除余额
func (s *SearchService) deductBalance(userID uint, amount float64) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取用户当前余额
	var user model.User
	if err := tx.Set("gorm:query_option", "FOR UPDATE").First(&user, userID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 检查余额
	if user.Balance < amount {
		tx.Rollback()
		return errors.New("余额不足")
	}

	// 扣除余额
	newBalance := user.Balance - amount
	if err := tx.Model(&user).Update("balance", newBalance).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 记录余额变动
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      -amount,
		Balance:     newBalance,
		Type:        2, // 消费
		Description: "拍照搜题扣费",
	}

	if err := tx.Create(balanceLog).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
