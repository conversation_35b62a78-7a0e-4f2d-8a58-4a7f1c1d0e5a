package service

import (
	"errors"
	"fmt"
	"solve_api_admin/internal/config"
	"solve_api_admin/internal/model"
	"solve_api_admin/pkg/utils"

	"gorm.io/gorm"
)

type AppService struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewAppService(db *gorm.DB, cfg *config.Config) *AppService {
	return &AppService{
		db:  db,
		cfg: cfg,
	}
}

// CreateApp 创建应用
func (s *AppService) CreateApp(userID uint, name string, appType int) (*model.App, error) {
	// 检查用户是否存在且状态正常
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}
	
	if user.Status != 1 {
		return nil, errors.New("账户已被冻结，无法创建应用")
	}

	// 检查应用数量限制
	var count int64
	s.db.Model(&model.App{}).Where("user_id = ?", userID).Count(&count)
	if count >= 5 {
		return nil, errors.New("每个用户最多只能创建5个应用")
	}

	// 生成应用密钥
	appKey := utils.GenerateAppKey()
	secretKey := utils.GenerateSecretKey()

	// 创建应用
	app := &model.App{
		UserID:    userID,
		Name:      name,
		Type:      appType,
		AppKey:    appKey,
		SecretKey: secretKey,
		Status:    1,
	}

	if err := s.db.Create(app).Error; err != nil {
		return nil, fmt.Errorf("创建应用失败: %v", err)
	}

	return app, nil
}

// GetUserApps 获取用户应用列表
func (s *AppService) GetUserApps(userID uint) ([]model.App, error) {
	var apps []model.App
	if err := s.db.Where("user_id = ?", userID).Find(&apps).Error; err != nil {
		return nil, fmt.Errorf("获取应用列表失败: %v", err)
	}

	// 列表接口不返回secret_key
	for i := range apps {
		apps[i].SecretKey = ""
	}

	return apps, nil
}

// GetAppDetail 获取应用详情
func (s *AppService) GetAppDetail(userID, appID uint) (*model.App, error) {
	var app model.App
	if err := s.db.Where("id = ? AND user_id = ?", appID, userID).First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("应用不存在")
		}
		return nil, fmt.Errorf("获取应用详情失败: %v", err)
	}

	return &app, nil
}

// UpdateApp 更新应用信息
func (s *AppService) UpdateApp(userID, appID uint, name string) (*model.App, error) {
	var app model.App
	if err := s.db.Where("id = ? AND user_id = ?", appID, userID).First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("应用不存在")
		}
		return nil, fmt.Errorf("查询应用失败: %v", err)
	}

	// 更新应用名称
	app.Name = name
	if err := s.db.Save(&app).Error; err != nil {
		return nil, fmt.Errorf("更新应用失败: %v", err)
	}

	return &app, nil
}

// ResetAppSecret 重置应用密钥
func (s *AppService) ResetAppSecret(userID, appID uint) (*model.App, error) {
	var app model.App
	if err := s.db.Where("id = ? AND user_id = ?", appID, userID).First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("应用不存在")
		}
		return nil, fmt.Errorf("查询应用失败: %v", err)
	}

	// 生成新的密钥
	app.SecretKey = utils.GenerateSecretKey()
	if err := s.db.Save(&app).Error; err != nil {
		return nil, fmt.Errorf("重置密钥失败: %v", err)
	}

	return &app, nil
}

// UpdateAppStatus 更新应用状态
func (s *AppService) UpdateAppStatus(userID, appID uint, status int) (*model.App, error) {
	var app model.App
	if err := s.db.Where("id = ? AND user_id = ?", appID, userID).First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("应用不存在")
		}
		return nil, fmt.Errorf("查询应用失败: %v", err)
	}

	// 更新状态
	app.Status = status
	if err := s.db.Save(&app).Error; err != nil {
		return nil, fmt.Errorf("更新应用状态失败: %v", err)
	}

	return &app, nil
}

// GetAppByKeys 根据密钥获取应用
func (s *AppService) GetAppByKeys(appKey, secretKey string) (*model.App, error) {
	var app model.App
	if err := s.db.Where("app_key = ? AND secret_key = ?", appKey, secretKey).First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("API密钥无效")
		}
		return nil, fmt.Errorf("查询应用失败: %v", err)
	}

	if app.Status != 1 {
		return nil, errors.New("应用已被冻结")
	}

	return &app, nil
}
