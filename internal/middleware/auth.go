package middleware

import (
	"net/http"
	"solve_api_admin/internal/service"
	"solve_api_admin/pkg/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(secret string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "未提供认证token",
				"data":    nil,
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token格式错误",
				"data":    nil,
			})
			c.Abort()
			return
		}

		// 解析token
		claims, err := utils.ParseJWT(tokenString, secret)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token无效",
				"data":    nil,
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("user_role", claims.Role)
		c.Next()
	}
}

// AdminMiddleware 管理员权限中间件
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("user_role")
		if !exists || role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "需要管理员权限",
				"data":    nil,
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// AppAuthMiddleware 应用认证中间件
func AppAuthMiddleware(appService *service.AppService) gin.HandlerFunc {
	return func(c *gin.Context) {
		appKey := c.GetHeader("X-App-Key")
		secretKey := c.GetHeader("X-Secret-Key")

		if appKey == "" || secretKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "缺少API密钥",
				"data":    nil,
			})
			c.Abort()
			return
		}

		// 验证应用密钥
		app, err := appService.GetAppByKeys(appKey, secretKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": err.Error(),
				"data":    nil,
			})
			c.Abort()
			return
		}

		// 将应用信息存储到上下文
		c.Set("app", app)
		c.Set("app_id", app.ID)
		c.Set("user_id", app.UserID)
		c.Next()
	}
}
