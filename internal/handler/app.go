package handler

import (
	"net/http"
	"solve_api_admin/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AppHandler struct {
	appService *service.AppService
}

func NewAppHandler(appService *service.AppService) *AppHandler {
	return &AppHandler{
		appService: appService,
	}
}

// CreateAppRequest 创建应用请求
type CreateAppRequest struct {
	Name string `json:"name" binding:"required,min=1,max=50"`
	Type int    `json:"type" binding:"required,min=1"`
}

// UpdateAppRequest 更新应用请求
type UpdateAppRequest struct {
	Name string `json:"name" binding:"required,min=1,max=50"`
}

// UpdateAppStatusRequest 更新应用状态请求
type UpdateAppStatusRequest struct {
	Status int `json:"status" binding:"required,oneof=1 2"`
}

// CreateApp 创建应用
func (h *AppHandler) CreateApp(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID格式错误",
			"data":    nil,
		})
		return
	}

	var req CreateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	app, err := h.appService.CreateApp(uint(userID), req.Name, req.Type)
	if err != nil {
		if err.Error() == "账户已被冻结，无法创建应用" {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": err.Error(),
				"data":    nil,
			})
			return
		}
		if err.Error() == "每个用户最多只能创建5个应用" {
			c.JSON(http.StatusConflict, gin.H{
				"code":    409,
				"message": err.Error(),
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "应用创建成功",
		"data":    app,
	})
}

// GetUserApps 获取用户应用列表
func (h *AppHandler) GetUserApps(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID格式错误",
			"data":    nil,
		})
		return
	}

	apps, err := h.appService.GetUserApps(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取应用列表成功",
		"data":    apps,
	})
}

// GetAppDetail 获取应用详情
func (h *AppHandler) GetAppDetail(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID格式错误",
			"data":    nil,
		})
		return
	}

	appIDStr := c.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "应用ID格式错误",
			"data":    nil,
		})
		return
	}

	app, err := h.appService.GetAppDetail(uint(userID), uint(appID))
	if err != nil {
		if err.Error() == "应用不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取应用详情成功",
		"data":    app,
	})
}

// UpdateApp 更新应用信息
func (h *AppHandler) UpdateApp(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID格式错误",
			"data":    nil,
		})
		return
	}

	appIDStr := c.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "应用ID格式错误",
			"data":    nil,
		})
		return
	}

	var req UpdateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	app, err := h.appService.UpdateApp(uint(userID), uint(appID), req.Name)
	if err != nil {
		if err.Error() == "应用不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "应用信息更新成功",
		"data":    app,
	})
}

// ResetAppSecret 重置应用密钥
func (h *AppHandler) ResetAppSecret(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID格式错误",
			"data":    nil,
		})
		return
	}

	appIDStr := c.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "应用ID格式错误",
			"data":    nil,
		})
		return
	}

	app, err := h.appService.ResetAppSecret(uint(userID), uint(appID))
	if err != nil {
		if err.Error() == "应用不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "SecretKey重置成功",
		"data":    app,
	})
}

// UpdateAppStatus 更新应用状态
func (h *AppHandler) UpdateAppStatus(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID格式错误",
			"data":    nil,
		})
		return
	}

	appIDStr := c.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "应用ID格式错误",
			"data":    nil,
		})
		return
	}

	var req UpdateAppStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"data":    nil,
		})
		return
	}

	app, err := h.appService.UpdateAppStatus(uint(userID), uint(appID), req.Status)
	if err != nil {
		if err.Error() == "应用不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "应用状态更新成功",
		"data":    app,
	})
}
