package handler

import (
	"net/http"
	"solve_api_admin/internal/model"
	"solve_api_admin/internal/service"

	"github.com/gin-gonic/gin"
)

type SearchHandler struct {
	searchService *service.SearchService
	appService    *service.AppService
}

func NewSearchHandler(searchService *service.SearchService, appService *service.AppService) *SearchHandler {
	return &SearchHandler{
		searchService: searchService,
		appService:    appService,
	}
}

// SearchRequest 搜题请求
type SearchRequest struct {
	ImageURL string `json:"image_url" binding:"required"`
}

// Search 拍照搜题
func (h *SearchHandler) Search(c *gin.Context) {
	var req SearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "图片URL格式错误或无法访问",
			"data":    nil,
		})
		return
	}

	// 从中间件获取应用信息
	app, exists := c.Get("app")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "API密钥无效",
			"data":    nil,
		})
		return
	}

	appModel := app.(*model.App)

	// 调用搜题服务
	result, err := h.searchService.Search(appModel, req.ImageURL)
	if err != nil {
		if err.Error() == "余额不足，请先充值" {
			c.JSON(http.StatusPaymentRequired, gin.H{
				"code":    402,
				"message": err.Error(),
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "搜题成功",
		"data":    result,
	})
}
