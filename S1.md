# 用户应用创建业务API接口文档

## 概述

本文档详细说明了用户应用创建、管理、配置等相关API接口，为前端开发团队提供完整的接入指南。系统支持多应用管理，每个应用拥有独立的API密钥，用于调用拍照搜题等核心业务功能。

## 基础信息

- **服务地址**: `http://localhost:8080`
- **API版本**: v1
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权/认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 用户认证接口

### 1. 用户注册

**接口地址**: `POST /api/v1/user/register`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "password123",
  "code": "123456",
  "invite_code": "SOLVE2024"
}
```

**参数说明**:
- `phone`: 手机号，11位数字
- `password`: 密码，6-20位，支持大小写字母、数字、常用标点符号
- `code`: 短信验证码，6位数字
- `invite_code`: 邀请码，默认为"SOLVE2024"

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 用户登录

**接口地址**: `POST /api/v1/user/login`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.50,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 发送注册验证码

**接口地址**: `POST /api/v1/user/send-code`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

## 应用管理接口

### 1. 创建应用

**接口地址**: `POST /api/v1/user/{user_id}/app`

**请求参数**:
```json
{
  "name": "我的搜题应用",
  "type": 1
}
```

**参数说明**:
- `name`: 应用名称，1-50个字符
- `type`: 应用类型，1=拍照搜题

**响应示例**:
```json
{
  "code": 200,
  "message": "应用创建成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

**错误响应**:
- `403`: 账户已被冻结，无法创建应用
- `409`: 每个用户最多只能创建5个应用

### 2. 获取应用列表

**接口地址**: `GET /api/v1/user/{user_id}/app`

**响应示例**:
```json
{
  "code": 200,
  "message": "获取应用列表成功",
  "data": [
    {
      "id": 1,
      "name": "我的搜题应用",
      "type": 1,
      "app_key": "app_1234567890abcdef",
      "status": 1,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**注意**: 列表接口不返回`secret_key`，需要通过详情接口获取

### 3. 获取应用详情

**接口地址**: `GET /api/v1/user/{user_id}/app/{app_id}`

**响应示例**:
```json
{
  "code": 200,
  "message": "获取应用详情成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 更新应用信息

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}`

**请求参数**:
```json
{
  "name": "新的应用名称"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "应用信息更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "新的应用名称",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5. 重置应用密钥

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/reset-secret`

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "SecretKey重置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_newkey1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 6. 更新应用状态

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/status`

**请求参数**:
```json
{
  "status": 2
}
```

**参数说明**:
- `status`: 应用状态，1=正常，2=冻结

**响应示例**:
```json
{
  "code": 200,
  "message": "应用状态更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "app_1234567890abcdef",
    "secret_key": "sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
    "status": 2,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 拍照搜题接口

### 1. 拍照搜题

**接口地址**: `POST /api/v1/api/search`

**请求头**:
```
Content-Type: application/json
X-App-Key: app_1234567890abcdef
X-Secret-Key: sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890
```

**请求参数**:
```json
{
  "image_url": "https://example.com/image.jpg"
}
```

**参数说明**:
- `image_url`: 图片URL地址，支持常见图片格式（jpg、png、gif、webp等）

**响应示例**:
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 1,
    "question": "解这个方程：2x + 3 = 7",
    "answer": "解：2x + 3 = 7\n2x = 7 - 3\n2x = 4\nx = 2",
    "analysis": "这是一个一元一次方程，通过移项和化简可以求解。",
    "cost": 0.1,
    "cached": false,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**错误响应**:
- `401`: API密钥无效或应用已冻结
- `402`: 余额不足
- `400`: 图片URL格式错误或无法访问

## 用户余额管理接口

### 1. 获取用户余额

**接口地址**: `GET /api/v1/user/{user_id}/balance`

**响应示例**:
```json
{
  "code": 200,
  "message": "获取余额成功",
  "data": {
    "balance": 100.50
  }
}
```

### 2. 用户充值

**接口地址**: `POST /api/v1/user/{user_id}/recharge`

**请求参数**:
```json
{
  "amount": 50.00,
  "description": "支付宝充值"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "充值成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "amount": 50.00,
    "balance": 150.50,
    "type": 1,
    "description": "支付宝充值",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 获取余额变动记录

**接口地址**: `GET /api/v1/user/{user_id}/balance/logs`

**查询参数**:
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `type`: 记录类型，1=充值，2=消费，3=退款

**响应示例**:
```json
{
  "code": 200,
  "message": "获取余额记录成功",
  "data": {
    "list": [
      {
        "id": 1,
        "amount": -0.1,
        "balance": 100.40,
        "type": 2,
        "description": "拍照搜题扣费",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20
  }
}
```

## 应用类型和状态说明

### 应用类型
- `1`: 拍照搜题 - 支持图片识别和题目解析

### 应用状态
- `1`: 正常 - 可以正常调用API
- `2`: 冻结 - 无法调用API，需要联系管理员

### 余额记录类型
- `1`: 充值 - 用户充值增加余额
- `2`: 消费 - 调用API扣费
- `3`: 退款 - 管理员退款

## 业务流程说明

### 1. 用户注册流程
1. 发送验证码 → `POST /api/v1/user/send-code`
2. 用户注册 → `POST /api/v1/user/register`
3. 用户登录 → `POST /api/v1/user/login`

### 2. 应用创建流程
1. 用户登录获取user_id
2. 创建应用 → `POST /api/v1/user/{user_id}/app`
3. 获取应用详情（包含密钥） → `GET /api/v1/user/{user_id}/app/{app_id}`

### 3. 拍照搜题流程
1. 确保用户余额充足 → `GET /api/v1/user/{user_id}/balance`
2. 使用应用密钥调用搜题API → `POST /api/v1/api/search`
3. 系统自动扣费并返回结果

### 4. 密钥管理流程
1. 查看当前密钥 → `GET /api/v1/user/{user_id}/app/{app_id}`
2. 重置密钥（如果泄露） → `PUT /api/v1/user/{user_id}/app/{app_id}/reset-secret`
3. 更新应用中的密钥配置

## 错误处理

### 常见错误码

#### 400 错误 - 请求参数错误
```json
{
  "code": 400,
  "message": "应用名称不能为空",
  "data": null
}
```

#### 401 错误 - 认证失败
```json
{
  "code": 401,
  "message": "API密钥无效",
  "data": null
}
```

#### 402 错误 - 余额不足
```json
{
  "code": 402,
  "message": "余额不足，请先充值",
  "data": null
}
```

#### 403 错误 - 权限不足
```json
{
  "code": 403,
  "message": "账户已被冻结，无法创建应用",
  "data": null
}
```

#### 404 错误 - 资源不存在
```json
{
  "code": 404,
  "message": "应用不存在",
  "data": null
}
```

#### 409 错误 - 资源冲突
```json
{
  "code": 409,
  "message": "每个用户最多只能创建5个应用",
  "data": null
}
```

## 限制说明

### 应用限制
- 每个用户最多创建 **5个应用**
- 应用名称长度：**1-50个字符**
- 目前仅支持拍照搜题类型应用

### API调用限制
- 默认限流：**10次/秒**
- 图片大小：建议不超过 **10MB**
- 支持格式：jpg、png、gif、webp等常见格式

### 余额限制
- 最小充值金额：**1元**
- 拍照搜题费用：**0.1元/次**（具体价格可能调整）

## 开发调试

### 启动服务
```bash
# 开发模式启动
./dev.sh

# 或者直接运行
go run cmd/main.go
```

### 测试接口示例

#### 1. 创建应用
```bash
curl -X POST http://localhost:8080/api/v1/user/1/app \
  -H "Content-Type: application/json" \
  -d '{"name":"测试应用","type":1}'
```

#### 2. 获取应用列表
```bash
curl -X GET http://localhost:8080/api/v1/user/1/app
```

#### 3. 拍照搜题
```bash
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -H "X-App-Key: app_1234567890abcdef" \
  -H "X-Secret-Key: sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890" \
  -d '{"image_url":"https://example.com/image.jpg"}'
```

#### 4. 用户充值
```bash
curl -X POST http://localhost:8080/api/v1/user/1/recharge \
  -H "Content-Type: application/json" \
  -d '{"amount":50.00,"description":"测试充值"}'
```

## 前端集成建议

### 1. 状态管理
- 保存用户登录状态和user_id
- 缓存应用列表，避免频繁请求
- 实时显示用户余额

### 2. 错误处理
- 统一处理API错误响应
- 余额不足时引导用户充值
- 应用被冻结时显示相应提示

### 3. 安全考虑
- 妥善保管应用密钥，不要在前端明文存储
- 建议在后端代理API调用，避免密钥泄露
- 定期检查应用状态

### 4. 用户体验
- 创建应用后自动跳转到应用详情
- 提供密钥重置功能的安全提示
- 显示应用使用统计和余额消费记录







---------------------------上面内容已开发完成-------------------

请帮我建立一个简单的html页面，集成到管理页中，这个功能是个完全独立的，并不依赖于登录状态。测试时，只需要输入一个url地址，点击提交即可完成。参考下方接入文档帮我实现这个功能
# 拍照搜题API独立接口文档

## 📸 接口概述

拍照搜题API是一个独立的图像识别和题目解析服务，支持上传图片进行题目识别、解析和答案生成。

## 🔗 接口信息

- **接口地址**: `POST /api/v1/api/search`
- **完整URL**: `http://localhost:8080/api/v1/api/search`
- **请求方法**: `POST`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔐 认证方式

### 方式1: 请求头认证（推荐）

**请求格式**:
```http
POST /api/v1/api/search HTTP/1.1
Host: localhost:8080
Content-Type: application/json
X-App-Key: your_app_key
X-Secret-Key: your_secret_key

{
  "image_url": "https://example.com/question.jpg"
}
```

**优势**: 符合HTTP标准，安全性高，不会被日志记录

### 方式2: 请求体认证（简化集成）

**请求格式**:
```http
POST /api/v1/api/search HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "app_key": "your_app_key",
  "secret_key": "your_secret_key",
  "image_url": "https://example.com/question.jpg"
}
```

**优势**: 集成简单，适合快速开发，移动端友好

### 方式3: 嵌套认证（结构化）

**请求格式**:
```http
POST /api/v1/api/search HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "auth": {
    "app_key": "your_app_key",
    "secret_key": "your_secret_key"
  },
  "image_url": "https://example.com/question.jpg"
}
```

**优势**: 结构清晰，认证信息与业务数据分离

## 📝 请求参数

### 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `image_url` | string | 图片URL地址，必须可公开访问 | `"https://example.com/question.jpg"` |

### 认证参数（根据认证方式选择）

| 参数名 | 类型 | 位置 | 说明 |
|--------|------|------|------|
| `app_key` | string | 请求头/请求体 | 应用密钥 |
| `secret_key` | string | 请求头/请求体 | 应用秘钥 |

### 图片要求

- **支持格式**: jpg、png、gif、webp、bmp
- **文件大小**: 建议1-10MB
- **图片质量**: 清晰可读，包含完整题目
- **访问要求**: 必须可公开访问，无需认证

## 📊 响应格式

### 成功响应 (HTTP 200)

```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 1,
    "content": "下列哪个是质数？",
    "question_type": "单选题",
    "question_text": "下列哪个是质数？",
    "options": {
      "A": "4",
      "B": "6",
      "C": "7",
      "D": "8"
    },
    "analysis": "质数是只能被1和自身整除的大于1的自然数。选项中：A.4=2×2，B.6=2×3，C.7只能被1和7整除，D.8=2×4。因此答案是C。",
    "answer": "C",
    "subject": "数学",
    "grade": "小学",
    "difficulty": 1,
    "source_model": "qwen-vl-plus,deepseek-chat",
    "cache_hit": false,
    "process_time": 1500
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `code` | number | 响应状态码，200表示成功 |
| `message` | string | 响应消息 |
| `data` | object | 搜题结果数据 |

#### data字段详细说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `id` | number | 题目唯一标识 | `1` |
| `content` | string | 题目内容（兼容字段） | `"下列哪个是质数？"` |
| `question_type` | string | 题目类型 | `"单选题"、"多选题"、"填空题"、"解答题"、"判断题"` |
| `question_text` | string | 结构化题目内容 | `"下列哪个是质数？"` |
| `options` | object | 选项内容（仅选择题有） | `{"A":"4","B":"6","C":"7","D":"8"}` |
| `analysis` | string | 详细解题思路和步骤 | `"质数是只能被1和自身整除..."` |
| `answer` | string | 最终答案 | `"C"` |
| `subject` | string | 学科分类 | `"数学"、"语文"、"英语"、"物理"等` |
| `grade` | string | 年级分类 | `"小学"、"初中"、"高中"、"大学"` |
| `difficulty` | number | 难度等级（1-5） | `1`（最简单）到`5`（最困难） |
| `source_model` | string | 使用的AI模型 | `"qwen-vl-plus,deepseek-chat"` |
| `cache_hit` | boolean | 是否命中缓存 | `true`/`false` |
| `process_time` | number | 处理时间（毫秒） | `1500` |

## ❌ 错误响应

### 错误响应格式

```json
{
  "code": 错误码,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 错误描述 | 解决方案 |
|--------|------------|----------|----------|
| 400 | 400 | 请求参数错误 | 检查图片URL格式和参数完整性 |
| 401 | 401 | API密钥无效 | 检查app_key和secret_key是否正确 |
| 402 | 402 | 余额不足 | 用户需要充值账户余额 |
| 403 | 403 | 应用被冻结 | 联系管理员解冻应用 |
| 404 | 404 | 图片无法访问 | 检查图片URL是否可访问 |
| 429 | 429 | 请求频率过高 | 降低请求频率，实现限流 |
| 500 | 500 | 服务器内部错误 | 稍后重试或联系技术支持 |

### 错误响应示例

#### 401 认证失败
```json
{
  "code": 401,
  "message": "API密钥无效",
  "data": null
}
```

#### 402 余额不足
```json
{
  "code": 402,
  "message": "余额不足，请先充值",
  "data": null
}
```

#### 400 参数错误
```json
{
  "code": 400,
  "message": "图片URL不能为空",
  "data": null
}
```

## 🚀 调用示例

### JavaScript/Fetch

#### 请求头认证
```javascript
const searchQuestionWithHeaders = async (imageUrl, appKey, secretKey) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': appKey,
        'X-Secret-Key': secretKey
      },
      body: JSON.stringify({
        image_url: imageUrl
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('搜题成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('搜题失败:', error);
    throw error;
  }
};
```

#### 请求体认证
```javascript
const searchQuestionWithBody = async (imageUrl, appKey, secretKey) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        app_key: appKey,
        secret_key: secretKey,
        image_url: imageUrl
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    throw error;
  }
};
```

#### 使用示例
```javascript
// 调用搜题API
searchQuestionWithBody(
  'https://example.com/question.jpg',
  'your_app_key',
  'your_secret_key'
).then(data => {
  console.log('题目类型:', data.question_type);
  console.log('题目内容:', data.question_text);
  console.log('选项:', data.options);
  console.log('答案:', data.answer);
  console.log('解析:', data.analysis);
}).catch(error => {
  console.error('搜题失败:', error.message);
});
```

### Python requests

```python
import requests
import json

def search_question(image_url, app_key, secret_key, auth_method='body'):
    """
    拍照搜题API调用
    
    Args:
        image_url (str): 图片URL
        app_key (str): 应用密钥
        secret_key (str): 应用秘钥
        auth_method (str): 认证方式 'header' 或 'body'
    
    Returns:
        dict: 搜题结果
    """
    url = "http://localhost:8080/api/v1/api/search"
    
    if auth_method == 'header':
        headers = {
            'Content-Type': 'application/json',
            'X-App-Key': app_key,
            'X-Secret-Key': secret_key
        }
        data = {
            'image_url': image_url
        }
    else:
        headers = {
            'Content-Type': 'application/json'
        }
        data = {
            'app_key': app_key,
            'secret_key': secret_key,
            'image_url': image_url
        }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        result = response.json()
        
        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(result['message'])
            
    except requests.exceptions.RequestException as e:
        raise Exception(f"请求失败: {e}")

# 使用示例
try:
    result = search_question(
        "https://example.com/question.jpg",
        "your_app_key",
        "your_secret_key"
    )
    
    print(f"题目类型: {result['question_type']}")
    print(f"题目内容: {result['question_text']}")
    print(f"答案: {result['answer']}")
    print(f"解析: {result['analysis']}")
    
except Exception as e:
    print(f"搜题失败: {e}")
```

### curl 命令

#### 请求头认证
```bash
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your_app_key" \
  -H "X-Secret-Key: your_secret_key" \
  -d '{
    "image_url": "https://example.com/question.jpg"
  }'
```

#### 请求体认证
```bash
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "app_key": "your_app_key",
    "secret_key": "your_secret_key",
    "image_url": "https://example.com/question.jpg"
  }'
```

## 💰 计费说明

### 计费规则
- **计费方式**: 按次计费
- **标准价格**: 0.1元/次
- **扣费时机**: 成功返回结果后扣费
- **缓存优化**: 相同题目命中缓存不重复扣费

### 余额管理
- 余额不足时返回402错误码
- 需要通过充值接口增加余额
- 可通过余额查询接口检查当前余额

## 🔧 性能优化

### 缓存机制
- 相同题目内容会命中缓存
- 缓存命中时`cache_hit`字段为`true`
- 缓存命中响应时间显著降低（通常<100ms）

### 请求优化建议
1. **超时设置**: 建议设置30秒超时
2. **重试机制**: 实现指数退避重试
3. **限流控制**: 避免频繁请求，建议10次/秒以内
4. **图片优化**: 压缩图片大小，提高传输效率

### 错误处理最佳实践
```javascript
const searchWithRetry = async (imageUrl, appKey, secretKey, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await searchQuestion(imageUrl, appKey, secretKey);
      return result;
    } catch (error) {
      if (error.code === 429 && i < maxRetries - 1) {
        // 频率限制，等待后重试
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        continue;
      }
      throw error;
    }
  }
};
```

## 📞 技术支持

### 常见问题
1. **Q**: 如何获取API密钥？
   **A**: 需要先注册用户，创建应用后获取app_key和secret_key

2. **Q**: 支持哪些图片格式？
   **A**: 支持jpg、png、gif、webp、bmp等常见格式

3. **Q**: 图片大小有限制吗？
   **A**: 建议1-10MB，过大可能影响处理速度

4. **Q**: 如何提高识别准确率？
   **A**: 使用清晰、完整、光线良好的题目图片

### 联系方式
- 📧 技术支持: <EMAIL>
- 📱 客服电话: 400-123-4567
- 💬 在线客服: 工作日 9:00-18:00

---


--------------------上面功能已经完成开发了----------------
# 配置管理API接口文档

## 📋 接口概述

本文档专门为前端开发团队提供配置管理相关API的完整接入指南，包括系统配置、模型配置、价格配置等管理功能。

## 🔗 基础信息

- **基础URL**: `http://localhost:8080/api/v1/admin`
- **认证方式**: Bearer Token（管理员权限）
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔐 认证说明

所有配置管理API都需要管理员权限，请在请求头中携带管理员token：

```http
Authorization: Bearer admin_token
Content-Type: application/json
```

## 🛠️ 系统配置管理

### 数据结构

#### SystemConfig 系统配置
```typescript
interface SystemConfig {
  id: number;
  key: string;            // 配置键名
  value: string;          // 配置值
  description: string;    // 配置描述
  created_at: string;
  updated_at: string;
}
```

#### 系统配置键常量
```typescript
const CONFIG_KEYS = {
  INVITE_CODE: 'invite_code',    // 邀请码
  RATE_LIMIT: 'rate_limit',      // 限流配置（次/秒）
  CACHE_TTL: 'cache_ttl'         // 缓存TTL（秒）
};
```

### API接口

#### 1. 获取系统信息（包含配置）
```http
GET /api/v1/admin/system/info
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取系统信息成功",
  "data": {
    "system_stats": {
      "total_users": 150,
      "total_apps": 45,
      "total_calls": 12580
    },
    "configs": {
      "invite_code": "SOLVE2024",
      "rate_limit": "10",
      "cache_ttl": "604800"
    },
    "version": "1.0.0",
    "build_time": "2024-01-01"
  }
}
```

#### 2. 批量更新系统配置
```http
PUT /api/v1/admin/system/config
```

**请求参数**:
```json
{
  "invite_code": "SOLVE2025",
  "rate_limit": "20",
  "cache_ttl": "86400"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "系统配置更新成功",
  "data": null
}
```

## 🤖 模型配置管理

### 数据结构

#### ModelConfig 模型配置
```typescript
interface ModelConfig {
  id: number;
  name: string;           // 模型名称，如 "qwen-vl-plus"
  api_url: string;        // API地址
  api_key: string;        // API密钥
  params: object;         // 参数配置对象
  status: number;         // 1=启用, 2=禁用
  status_name: string;    // 状态名称
  created_at: string;
  updated_at: string;
}
```

#### 模型参数配置示例
```json
{
  "temperature": 0.3,
  "max_tokens": 1500,
  "top_p": 0.8,
  "response_format": {"type": "json_object"},
  "detail": "high"
}
```

### API接口

#### 1. 创建模型配置
```http
POST /api/v1/admin/model
```

**请求参数**:
```json
{
  "name": "qwen-vl-plus",
  "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
  "api_key": "sk-xxx",
  "params": {
    "temperature": 0.3,
    "max_tokens": 1500,
    "top_p": 0.8,
    "response_format": {"type": "json_object"},
    "detail": "high"
  },
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型配置创建成功",
  "data": {
    "id": 1,
    "name": "qwen-vl-plus",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-xxx",
    "params": {
      "temperature": 0.3,
      "max_tokens": 1500,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 2. 获取模型配置列表
```http
GET /api/v1/admin/model?page=1&page_size=10
```

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | number | 否 | 页码 | 1 |
| page_size | number | 否 | 每页数量 | 10 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取模型配置列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "qwen-vl-plus",
        "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
        "status": 1,
        "status_name": "启用",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

#### 3. 获取启用的模型配置
```http
GET /api/v1/admin/model/enabled
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取启用的模型配置成功",
  "data": [
    {
      "id": 1,
      "name": "qwen-vl-plus",
      "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
      "status": 1,
      "status_name": "启用",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

#### 4. 获取模型配置详情
```http
GET /api/v1/admin/model/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取模型配置成功",
  "data": {
    "id": 1,
    "name": "qwen-vl-plus",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-xxx",
    "params": {
      "temperature": 0.3,
      "max_tokens": 1500,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 5. 更新模型配置
```http
PUT /api/v1/admin/model/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**请求参数**（所有字段都是可选的）:
```json
{
  "name": "qwen-vl-plus-v2",
  "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
  "api_key": "sk-new-key",
  "params": {
    "temperature": 0.5,
    "max_tokens": 2000
  },
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型配置更新成功",
  "data": {
    "id": 1,
    "name": "qwen-vl-plus-v2",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-new-key",
    "params": {
      "temperature": 0.5,
      "max_tokens": 2000,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T11:00:00Z"
  }
}
```

#### 6. 更新模型状态
```http
PUT /api/v1/admin/model/{id}/status
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**请求参数**:
```json
{
  "status": 2
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型状态更新成功",
  "data": null
}
```

#### 7. 删除模型配置
```http
DELETE /api/v1/admin/model/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 模型配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "模型配置删除成功",
  "data": null
}
```

## 💰 价格配置管理

### 数据结构

#### PriceConfig 价格配置
```typescript
interface PriceConfig {
  id: number;
  service_type: number;   // 服务类型：1=拍照搜题
  service_name: string;   // 服务名称
  price: number;          // 价格（元）
  user_id?: number;       // 用户ID（用户专属价格，null为默认价格）
  is_default: boolean;    // 是否为默认价格
  status: number;         // 1=启用, 2=禁用
  status_name: string;    // 状态名称
  created_at: string;
  updated_at: string;
}
```

#### 服务类型常量
```typescript
const SERVICE_TYPES = {
  PHOTO_SEARCH: 1    // 拍照搜题服务
};
```

### API接口

#### 1. 创建价格配置
```http
POST /api/v1/admin/price
```

**请求参数**:
```json
{
  "service_type": 1,
  "price": 0.1,
  "user_id": null,
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "价格配置创建成功",
  "data": {
    "id": 1,
    "service_type": 1,
    "service_name": "拍照搜题",
    "price": 0.1,
    "user_id": null,
    "is_default": true,
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 2. 获取价格配置列表
```http
GET /api/v1/admin/price?page=1&page_size=10
```

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | number | 否 | 页码 | 1 |
| page_size | number | 否 | 每页数量 | 10 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取价格配置列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "service_type": 1,
        "service_name": "拍照搜题",
        "price": 0.1,
        "user_id": null,
        "is_default": true,
        "status": 1,
        "status_name": "启用",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

#### 3. 获取价格配置详情
```http
GET /api/v1/admin/price/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 价格配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取价格配置成功",
  "data": {
    "id": 1,
    "service_type": 1,
    "service_name": "拍照搜题",
    "price": 0.1,
    "user_id": null,
    "is_default": true,
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 4. 更新价格配置
```http
PUT /api/v1/admin/price/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 价格配置ID |

**请求参数**（所有字段都是可选的）:
```json
{
  "service_type": 1,
  "price": 0.15,
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "价格配置更新成功",
  "data": {
    "id": 1,
    "service_type": 1,
    "service_name": "拍照搜题",
    "price": 0.15,
    "user_id": null,
    "is_default": true,
    "status": 1,
    "status_name": "启用",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T11:00:00Z"
  }
}
```

#### 5. 删除价格配置
```http
DELETE /api/v1/admin/price/{id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 价格配置ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "价格配置删除成功",
  "data": null
}
```

#### 6. 根据服务类型获取价格
```http
GET /api/v1/admin/price/service/{service_type}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| service_type | number | 是 | 服务类型 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取服务价格成功",
  "data": {
    "service_type": 1,
    "service_name": "拍照搜题",
    "default_price": 0.1,
    "user_prices": [
      {
        "user_id": 123,
        "price": 0.08,
        "username": "test_user"
      }
    ]
  }
}
```

#### 7. 设置默认价格
```http
PUT /api/v1/admin/price/service/{service_type}/default
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| service_type | number | 是 | 服务类型 |

**请求参数**:
```json
{
  "price": 0.12
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "默认价格设置成功",
  "data": null
}
```

#### 8. 设置用户专属价格
```http
PUT /api/v1/admin/price/service/{service_type}/user/{user_id}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| service_type | number | 是 | 服务类型 |
| user_id | number | 是 | 用户ID |

**请求参数**:
```json
{
  "price": 0.08
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "用户专属价格设置成功",
  "data": null
}
```

## ❌ 错误响应

### 错误响应格式
```json
{
  "code": 错误码,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 错误描述 | 解决方案 |
|--------|------------|----------|----------|
| 400 | 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 401 | 未授权访问 | 检查管理员token是否有效 |
| 403 | 403 | 权限不足 | 确认当前用户具有管理员权限 |
| 404 | 404 | 资源不存在 | 检查配置ID是否正确 |
| 409 | 409 | 资源冲突 | 检查是否存在重复的配置名称 |
| 500 | 500 | 服务器内部错误 | 稍后重试或联系技术支持 |

### 错误响应示例

#### 401 未授权
```json
{
  "code": 401,
  "message": "未授权访问",
  "data": null
}
```

#### 404 配置不存在
```json
{
  "code": 404,
  "message": "模型配置不存在",
  "data": null
}
```

#### 409 名称冲突
```json
{
  "code": 409,
  "message": "模型名称已存在",
  "data": null
}
```

## 🚀 调用示例

### JavaScript/Fetch

#### 获取模型配置列表
```javascript
const getModelConfigs = async (page = 1, pageSize = 10) => {
  try {
    const response = await fetch(`http://localhost:8080/api/v1/admin/model?page=${page}&page_size=${pageSize}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + adminToken,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('模型配置列表:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取模型配置失败:', error);
    throw error;
  }
};
```

#### 创建模型配置
```javascript
const createModelConfig = async (modelData) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/admin/model', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + adminToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(modelData)
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('模型配置创建成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建模型配置失败:', error);
    throw error;
  }
};

// 使用示例
createModelConfig({
  name: 'qwen-vl-plus',
  api_url: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
  api_key: 'sk-xxx',
  params: {
    temperature: 0.3,
    max_tokens: 1500,
    top_p: 0.8,
    response_format: {type: 'json_object'},
    detail: 'high'
  },
  status: 1
});
```

#### 更新系统配置
```javascript
const updateSystemConfig = async (configs) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/admin/system/config', {
      method: 'PUT',
      headers: {
        'Authorization': 'Bearer ' + adminToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configs)
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('系统配置更新成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新系统配置失败:', error);
    throw error;
  }
};

// 使用示例
updateSystemConfig({
  invite_code: 'SOLVE2025',
  rate_limit: '20',
  cache_ttl: '86400'
});
```

### Python requests

```python
import requests
import json

class ConfigAPI:
    def __init__(self, base_url, admin_token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {admin_token}',
            'Content-Type': 'application/json'
        }

    def get_model_configs(self, page=1, page_size=10):
        """获取模型配置列表"""
        url = f"{self.base_url}/admin/model"
        params = {'page': page, 'page_size': page_size}

        try:
            response = requests.get(url, headers=self.headers, params=params)
            result = response.json()

            if result['code'] == 200:
                return result['data']
            else:
                raise Exception(result['message'])

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

    def create_model_config(self, model_data):
        """创建模型配置"""
        url = f"{self.base_url}/admin/model"

        try:
            response = requests.post(url, headers=self.headers, json=model_data)
            result = response.json()

            if result['code'] == 200:
                return result['data']
            else:
                raise Exception(result['message'])

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

    def update_system_config(self, configs):
        """更新系统配置"""
        url = f"{self.base_url}/admin/system/config"

        try:
            response = requests.put(url, headers=self.headers, json=configs)
            result = response.json()

            if result['code'] == 200:
                return True
            else:
                raise Exception(result['message'])

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

# 使用示例
api = ConfigAPI('http://localhost:8080/api/v1', 'your_admin_token')

try:
    # 获取模型配置列表
    models = api.get_model_configs(page=1, page_size=10)
    print(f"模型配置数量: {models['total']}")

    # 创建新的模型配置
    new_model = api.create_model_config({
        'name': 'deepseek-chat',
        'api_url': 'https://api.deepseek.com/v1/chat/completions',
        'api_key': 'sk-xxx',
        'params': {
            'temperature': 0.3,
            'max_tokens': 2500,
            'top_p': 0.8,
            'response_format': {'type': 'json_object'}
        },
        'status': 1
    })
    print(f"新模型配置ID: {new_model['id']}")

    # 更新系统配置
    api.update_system_config({
        'invite_code': 'SOLVE2025',
        'rate_limit': '20'
    })
    print("系统配置更新成功")

except Exception as e:
    print(f"操作失败: {e}")
```

### curl 命令

#### 获取模型配置列表
```bash
curl -X GET "http://localhost:8080/api/v1/admin/model?page=1&page_size=10" \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json"
```

#### 创建模型配置
```bash
curl -X POST http://localhost:8080/api/v1/admin/model \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "qwen-vl-plus",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
    "api_key": "sk-xxx",
    "params": {
      "temperature": 0.3,
      "max_tokens": 1500,
      "top_p": 0.8,
      "response_format": {"type": "json_object"},
      "detail": "high"
    },
    "status": 1
  }'
```

#### 更新系统配置
```bash
curl -X PUT http://localhost:8080/api/v1/admin/system/config \
  -H "Authorization: Bearer your_admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "invite_code": "SOLVE2025",
    "rate_limit": "20",
    "cache_ttl": "86400"
  }'
```

## 📞 技术支持

### 常见问题

1. **Q**: 如何获取管理员token？
   **A**: 需要先通过管理员登录接口获取token

2. **Q**: 模型参数配置有什么限制？
   **A**: 参数必须是有效的JSON格式，具体参数取决于对应的AI模型

3. **Q**: 价格配置的精度是多少？
   **A**: 支持到小数点后4位，最小单位0.0001元

4. **Q**: 系统配置更新后是否立即生效？
   **A**: 是的，配置更新后立即生效，无需重启服务

### 联系方式
- 📧 技术支持: <EMAIL>
- 📱 客服电话: 400-123-4567
- 💬 在线客服: 工作日 9:00-18:00

---

**注意**: 配置管理功能仅限管理员使用，请妥善保管管理员凭证。生产环境请使用HTTPS协议确保数据传输安全。
