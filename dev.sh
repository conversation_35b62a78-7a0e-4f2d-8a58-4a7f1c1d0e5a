#!/bin/bash

# 开发环境启动脚本

echo "🚀 启动 Solve API Admin 开发服务器..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.21+"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
REQUIRED_VERSION="1.21"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Go 版本过低，需要 1.21+，当前版本: $GO_VERSION"
    exit 1
fi

# 创建数据目录
mkdir -p data

# 设置环境变量
export GIN_MODE=debug
export SERVER_PORT=8080
export DATABASE_DSN=data/solve_api.db
export REDIS_ADDR=localhost:6379
export JWT_SECRET=solve_api_secret_key_2024
export SMS_PROVIDER=mock

echo "📦 下载依赖..."
go mod tidy

echo "🔧 构建项目..."
go build -o bin/solve_api_admin cmd/main.go

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 构建成功"
echo "🌐 启动服务器在 http://localhost:8080"
echo "📚 API文档: http://localhost:8080/api/v1"
echo "🔧 前端界面: http://localhost:8000 (需要单独启动)"
echo ""
echo "测试账号:"
echo "  管理员: 15688515913 / admin888"
echo "  用户:   13800138000 / password123"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动服务器
./bin/solve_api_admin
