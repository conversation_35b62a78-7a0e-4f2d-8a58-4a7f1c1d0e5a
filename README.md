# API系统Web管理界面

一个简单的Web管理界面，用于测试后端API系统的各种功能。

## 🚀 快速开始

### 启动服务
```bash
# 在项目根目录下运行
python3 -m http.server 8000
```

### 访问界面
打开浏览器访问：http://localhost:8000

## 🔑 测试账号

### 管理员账号
- **手机号**: 15688515913
- **密码**: admin888
- **权限**: 超级管理员

### 用户账号
- **手机号**: 13800138000
- **密码**: password123
- **权限**: 普通用户

### 验证码测试
- **测试手机号**: 15653259315
- **固定验证码**: 123456（Redis不可用时）
- **邀请码**: SOLVE2024

## 📋 主要功能

### 用户功能
- ✅ 用户登录/注册
- ✅ 密码修改和重置
- ✅ 应用管理（创建、编辑、删除）
- ✅ 密钥管理（查看、复制、重置）

### 管理员功能
- ✅ 管理员登录
- ✅ 密码管理
- 🔄 用户管理（开发中）
- 🔄 系统设置（开发中）

### API测试工具
- ✅ 支持GET/POST/PUT/DELETE请求
- ✅ 自定义请求头和请求体
- ✅ 实时响应显示
- ✅ API文档展示
- ✅ **拍照搜题测试**（独立功能，无需登录）

## 🛠️ 技术栈

- **前端**: 纯HTML + CSS + JavaScript
- **架构**: 单页面应用，模块化设计
- **样式**: 响应式设计，现代化UI
- **状态管理**: localStorage + 内存状态

## 📁 项目结构

```
├── index.html          # 主界面
├── styles.css          # 样式文件
├── script.js           # 主要逻辑
├── api.js              # API调用封装
├── auth.js             # 认证相关
├── utils.js            # 工具函数
├── S1.md              # 需求文档
├── ccpp.md            # 开发记录
└── README.md          # 说明文档
```

## 🔧 开发说明

### API配置
- **服务地址**: `http://localhost:8080`
- **API版本**: v1
- **内容类型**: `application/json`

### 数据验证规则
- **手机号**: 11位数字的中国大陆手机号
- **密码**: 6-20位，支持大小写字母、数字、常用标点符号
- **验证码**: 6位数字，有效期5分钟

## 📖 使用指南

1. **登录系统**: 点击右上角登录按钮，或使用快速登录
2. **用户注册**: 点击"用户注册"按钮，填写信息并获取验证码
3. **密码重置**: 点击"重置密码"按钮，选择用户或管理员模式
4. **应用管理**: 登录后点击"应用管理"创建和管理应用
5. **API测试**: 使用"API测试工具"发送测试请求
6. **拍照搜题测试**: 点击"拍照搜题测试"进行独立API测试

## 📸 拍照搜题测试功能

### 功能特点
- **完全独立**: 无需登录即可使用
- **三种认证方式**: 请求头认证（推荐）、请求体认证、嵌套认证
- **友好界面**: 现代化UI设计，支持密码显示/隐藏
- **示例数据**: 一键填入测试数据
- **结果展示**: 格式化结果和原始JSON双重展示

### 使用步骤
1. **访问功能**: 主界面 → API测试 → 拍照搜题测试
2. **填入数据**:
   - 图片URL: 输入可公开访问的图片地址
   - App Key: 输入应用密钥
   - Secret Key: 输入应用秘钥
   - 认证方式: 选择认证方式（推荐请求头认证）
3. **快速测试**: 点击"填入示例"按钮快速填入测试数据
4. **开始搜题**: 点击"开始搜题"按钮发送请求
5. **查看结果**: 在格式化结果和原始JSON之间切换查看

### 认证方式说明
- **请求头认证**: 将密钥放在HTTP请求头中（X-App-Key, X-Secret-Key）
- **请求体认证**: 将密钥直接放在请求体中
- **嵌套认证**: 将密钥放在auth对象中，结构更清晰

### 示例数据
```
图片URL: https://example.com/question.jpg
App Key: app_1234567890abcdef
Secret Key: sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890
```

## 🔧 API连接测试

### 检查后端服务状态
1. **前端已启动**: http://localhost:8000 ✅
2. **后端需要**: http://localhost:8080 ❓

### 测试API连接
在浏览器控制台运行以下代码：
```javascript
// 加载测试脚本
const script = document.createElement('script');
script.src = './test_api.js';
document.head.appendChild(script);
```

### 如果API连接失败
1. **检查后端服务**: 确保有服务在8080端口运行
2. **查看控制台**: 检查具体的错误信息
3. **CORS问题**: 后端需要配置跨域访问

## 🐛 问题反馈

如果遇到问题，请检查：
1. 浏览器控制台是否有错误信息
2. 后端API服务是否正常运行 (localhost:8080)
3. 网络连接是否正常
4. 使用API测试工具检查具体接口

## 📝 更新日志

### v1.0.0 (2024-12-07)
- ✅ 完成基础框架搭建
- ✅ 实现用户认证系统
- ✅ 实现密码管理功能
- ✅ 实现应用管理功能
- ✅ 实现API测试工具
- ✅ 集成S1.md需求更新
- ✅ **新增拍照搜题测试功能**

### v1.1.0 (2024-12-07) - 拍照搜题测试功能
- ✅ 独立的拍照搜题API测试工具
- ✅ 支持三种认证方式（请求头/请求体/嵌套）
- ✅ 现代化UI设计和用户体验
- ✅ 格式化结果展示和原始JSON切换
- ✅ 完善的错误处理和解决方案提示
- ✅ 一键示例数据填入功能

---

更多详细信息请查看 [ccpp.md](./ccpp.md) 开发记录文档。
