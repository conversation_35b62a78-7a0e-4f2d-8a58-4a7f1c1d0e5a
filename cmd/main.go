package main

import (
	"log"
	"solve_api_admin/internal/config"
	"solve_api_admin/internal/database"
	"solve_api_admin/internal/router"
	"solve_api_admin/internal/service"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Init(cfg.Database.DSN)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 初始化Redis
	redisClient := database.InitRedis(cfg.Redis)

	// 初始化服务
	services := service.NewServices(db, redisClient, cfg)

	// 初始化路由
	r := router.Setup(services, cfg)

	// 启动服务器
	log.Printf("服务器启动在端口 %s", cfg.Server.Port)
	if err := r.Run(":" + cfg.Server.Port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
