package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// GenerateAppKey 生成应用密钥
func GenerateAppKey() string {
	// 生成格式: app_xxxxxxxxxxxxxxxx (16位随机字符)
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return fmt.Sprintf("app_%s", hex.EncodeToString(bytes))
}

// GenerateSecretKey 生成应用密钥
func GenerateSecretKey() string {
	// 生成格式: sk_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (64位随机字符)
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return fmt.Sprintf("sk_%s", hex.EncodeToString(bytes))
}

// GenerateVerifyCode 生成验证码
func GenerateVerifyCode() string {
	// 生成6位数字验证码
	bytes := make([]byte, 3)
	rand.Read(bytes)
	code := ""
	for _, b := range bytes {
		code += fmt.Sprintf("%02d", b%100)
	}
	return code[:6]
}

// GenerateUUID 生成UUID
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateOrderNo 生成订单号
func GenerateOrderNo() string {
	return fmt.Sprintf("%d%s", time.Now().Unix(), GenerateRandomString(6))
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) string {
	bytes := make([]byte, length/2+1)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)[:length]
}
